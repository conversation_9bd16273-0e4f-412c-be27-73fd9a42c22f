<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>木箱开料计算器</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        } 
        .container { 
            max-width: 1200px; 
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background-color: #fff;
            border-bottom: 1px solid #e0e0e0;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8em;
            font-weight: 600;
            color: #333;
        }
        .header p {
            margin: 8px 0 0 0;
            color: #666;
            font-size: 0.9em;
        }
        .content {
            padding: 30px;
        }
        .top-row {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
        }
        .left-panel {
            flex: 1;
        }
        .right-panel {
            flex: 1;
        }
        .input-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
        }
        .input-group { 
            margin-bottom: 15px; 
            display: flex;
            align-items: center;
        } 
        label { 
            display: inline-block; 
            width: 140px; 
            font-weight: 500;
            color: #555;
        } 
        input { 
            width: 120px; 
            padding: 8px 12px; 
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        input:focus {
            outline: none;
            border-color: #007acc;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button { 
            padding: 10px 20px; 
            border: none; 
            border-radius: 4px;
            cursor: pointer; 
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .btn-primary {
            background-color: #007acc;
            color: white;
        }
        .btn-primary:hover { 
            background-color: #005a9e;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        #result { 
            padding: 20px; 
            background-color: white;
            border-radius: 6px; 
            border: 1px solid #e0e0e0;
            margin-bottom: 20px;
        } 
        #calculation-process { 
            padding: 20px; 
            background-color: #f8f9fa;
            border-radius: 6px; 
            border: 1px solid #e0e0e0;
            transition: all 0.2s ease;
            margin-top: 10px;
        } 
        .part { 
            margin-bottom: 6px; 
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #007acc;
            font-size: 14px;
        } 
        .process-step { 
            margin-bottom: 12px; 
            padding: 12px; 
            background: white; 
            border-radius: 4px; 
            border: 1px solid #e0e0e0;
            font-size: 14px;
        } 
        .formula { 
            font-family: 'Courier New', monospace; 
            background-color: #f0f0f0; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-weight: 600;
            color: #d63384;
        } 
        .hidden {
            display: none;
        }
        .situation-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #2196F3;
        }
        .category {
            margin-bottom: 20px;
        }
        .category h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-weight: 600;
        }
        h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }
        @media (max-width: 768px) {
            .container { margin: 10px; }
            .content { padding: 20px; }
            .top-row { flex-direction: column; gap: 20px; }
            .input-group { flex-direction: column; align-items: flex-start; }
            label { width: auto; margin-bottom: 5px; }
            input { width: 100%; }
            .button-group { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>木箱开料计算器</h1>
            <p>基于长度自动选择最优计算方案</p>
        </div>
        
        <div class="content">
            <!-- 第一行：输入参数和开料清单 -->
            <div class="top-row">
                <div class="left-panel">
                    <!-- 输入参数区域 -->
                    <div class="input-section">
                        <h3>输入参数</h3>
                        <div class="input-group"> 
                            <label for="length">长度 C (mm):</label> 
                            <input type="number" id="length" value="2000"> 
                        </div> 
                        <div class="input-group"> 
                            <label for="width">宽度 K (mm):</label> 
                            <input type="number" id="width" value="800"> 
                        </div> 
                        <div class="input-group"> 
                            <label for="height">高度 G (mm):</label> 
                            <input type="number" id="height" value="600"> 
                        </div> 
                        <div class="input-group"> 
                            <label for="thickness">厚度 H (mm):</label> 
                            <input type="number" id="thickness" value="18"> 
                        </div> 
                    </div>
                 
                    <!-- 按钮组 -->
                    <div class="button-group">
                        <button class="btn-primary" onclick="calculate()">计算开料尺寸</button>
                        <button class="btn-secondary" onclick="toggleProcess()" id="toggleBtn">显示计算过程</button>
                    </div>
                </div>
                
                <div class="right-panel">
                    <!-- 显示结果 --> 
                    <div id="result"> 
                        <h3>开料清单</h3> 
                        <div id="resultList"></div> 
                    </div>
                </div>
            </div>
            
            <!-- 第二行：计算过程 -->
            <div id="calculation-process" class="hidden">
                <h3>计算过程</h3>
                <div id="processSteps"></div>
            </div>
        </div>
    </div>

    <script>
        // 根据长度判断使用哪种计算方案
        function getSituation(C) {
            if (C < 1500) return 1;
            if (C < 2440) return 2;
            return 3;
        }

        // 计算开料清单
        function calculateParts(C, K, G, H) {
            const situation = getSituation(C);
            let parts = [];
            
            if (situation === 1) {
                // 情况1: C < 1500mm
                parts = [
                    { category: "主要板材", name: "上下板", length: C + 2*H, width: K + 2*H, qty: 2 },
                    { category: "主要板材", name: "左右板", length: C + 2*H, width: G, qty: 2 },
                    { category: "主要板材", name: "前后板", length: K, width: G, qty: 2 },
                    { category: "辅助板材", name: "箱子支撑竖版", length: 10*H + K, width: 50, qty: 4 },
                    { category: "辅助板材", name: "盖子手抓板", length: K + 2*H, width: 50, qty: 2 },
                    { category: "辅助板材", name: "支腿", length: K + 2*H, width: 50, qty: 2 }
                ];
            } else if (situation === 2) {
                // 情况2: 1500mm ≤ C < 2440mm
                parts = [
                    { category: "主要板材", name: "上下板", length: C + 4*H, width: K + 2*H, qty: 2 },
                    { category: "主要板材", name: "左右板", length: C + 4*H, width: G, qty: 2 },
                    { category: "主要板材", name: "前后板", length: K, width: G, qty: 4 },
                    { category: "辅助板材", name: "箱子支撑竖版", length: 10*H + K, width: 50, qty: 6 },
                    { category: "辅助板材", name: "盖子手抓板", length: K + 2*H, width: 50, qty: 3 },
                    { category: "辅助板材", name: "支腿", length: K + 2*H, width: 50, qty: 3 }
                ];
            } else {
                // 情况3: C ≥ 2440mm
                parts = [
                    { category: "主要板材", name: "上下板", length: C + 4*H, width: K + 2*H, qty: 2 },
                    { category: "主要板材", name: "左右板", length: C + 4*H, width: G, qty: 2 },
                    { category: "主要板材", name: "前后板", length: K, width: G, qty: 4 },
                    { category: "辅助板材", name: "箱子支撑竖版", length: 10*H + K, width: 50, qty: 8 },
                    { category: "辅助板材", name: "盖子手抓板", length: K + 2*H, width: 50, qty: 4 },
                    { category: "辅助板材", name: "支腿", length: K + 2*H, width: 50, qty: 4 }
                ];
            }
            
            return { situation, parts };
        }

        // 生成计算过程说明
        function generateCalculationProcess(C, K, G, H, situation) {
            let processHtml = '';

            // 基本参数
            processHtml += `<div class="process-step">
                <strong>输入参数:</strong><br>
                长度 C = ${C} mm<br>
                宽度 K = ${K} mm<br>
                高度 G = ${G} mm<br>
                厚度 H = ${H} mm
            </div>`;

            // 情况判断
            let situationDesc = '';
            if (situation === 1) {
                situationDesc = `C = ${C}mm < 1500mm，适用情况1`;
            } else if (situation === 2) {
                situationDesc = `1500mm ≤ C = ${C}mm < 2440mm，适用情况2`;
            } else {
                situationDesc = `C = ${C}mm ≥ 2440mm，适用情况3`;
            }

            processHtml += `<div class="process-step">
                <strong>情况判断:</strong><br>
                ${situationDesc}
            </div>`;

            // 计算公式说明
            if (situation === 1) {
                processHtml += `<div class="process-step">
                    <strong>主要板材计算公式:</strong><br>
                    • 上下板: <span class="formula">(C+2H) × (K+2H) = (${C}+${2*H}) × (${K}+${2*H}) = ${C+2*H} × ${K+2*H}</span><br>
                    • 左右板: <span class="formula">(C+2H) × G = (${C}+${2*H}) × ${G} = ${C+2*H} × ${G}</span><br>
                    • 前后板: <span class="formula">K × G = ${K} × ${G}</span><br><br>
                    <strong>辅助板材计算公式:</strong><br>
                    • 箱子支撑竖版: <span class="formula">(10H+K) × 50 = (${10*H}+${K}) × 50 = ${10*H+K} × 50</span><br>
                    • 盖子手抓板: <span class="formula">(K+2H) × 50 = (${K}+${2*H}) × 50 = ${K+2*H} × 50</span><br>
                    • 支腿: <span class="formula">(K+2H) × 50 = (${K}+${2*H}) × 50 = ${K+2*H} × 50</span>
                </div>`;
            } else {
                processHtml += `<div class="process-step">
                    <strong>主要板材计算公式:</strong><br>
                    • 上下板: <span class="formula">(C+4H) × (K+2H) = (${C}+${4*H}) × (${K}+${2*H}) = ${C+4*H} × ${K+2*H}</span><br>
                    • 左右板: <span class="formula">(C+4H) × G = (${C}+${4*H}) × ${G} = ${C+4*H} × ${G}</span><br>
                    • 前后板: <span class="formula">K × G = ${K} × ${G}</span><br><br>
                    <strong>辅助板材计算公式:</strong><br>
                    • 箱子支撑竖版: <span class="formula">(10H+K) × 50 = (${10*H}+${K}) × 50 = ${10*H+K} × 50</span><br>
                    • 盖子手抓板: <span class="formula">(K+2H) × 50 = (${K}+${2*H}) × 50 = ${K+2*H} × 50</span><br>
                    • 支腿: <span class="formula">(K+2H) × 50 = (${K}+${2*H}) × 50 = ${K+2*H} × 50</span>
                </div>`;
            }

            return processHtml;
        }

        // 切换计算过程显示/隐藏
        function toggleProcess() {
            const processDiv = document.getElementById('calculation-process');
            const toggleBtn = document.getElementById('toggleBtn');

            if (processDiv.classList.contains('hidden')) {
                processDiv.classList.remove('hidden');
                toggleBtn.textContent = '隐藏计算过程';
            } else {
                processDiv.classList.add('hidden');
                toggleBtn.textContent = '显示计算过程';
            }
        }

        // 主计算函数
        function calculate() {
            // 1. 获取用户输入
            const C = parseFloat(document.getElementById('length').value);
            const K = parseFloat(document.getElementById('width').value);
            const G = parseFloat(document.getElementById('height').value);
            const H = parseFloat(document.getElementById('thickness').value);

            // 2. 计算板材清单
            const result = calculateParts(C, K, G, H);

            // 3. 生成情况说明
            let situationDesc = '';
            if (result.situation === 1) {
                situationDesc = `当前适用情况1 (C < 1500mm)`;
            } else if (result.situation === 2) {
                situationDesc = `当前适用情况2 (1500mm ≤ C < 2440mm)`;
            } else {
                situationDesc = `当前适用情况3 (C ≥ 2440mm)`;
            }

            // 4. 按类别分组显示结果
            let resultHtml = `<div class="situation-info">${situationDesc}</div>`;

            const categories = ["主要板材", "辅助板材"];
            categories.forEach(category => {
                const categoryParts = result.parts.filter(part => part.category === category);
                if (categoryParts.length > 0) {
                    resultHtml += `<div class="category">
                        <h4>${category}</h4>`;
                    categoryParts.forEach(part => {
                        resultHtml += `<div class="part"><strong>${part.name}</strong>: ${part.length} mm × ${part.width} mm (数量: ${part.qty})</div>`;
                    });
                    resultHtml += `</div>`;
                }
            });

            document.getElementById('resultList').innerHTML = resultHtml;

            // 5. 生成并显示计算过程
            const processHtml = generateCalculationProcess(C, K, G, H, result.situation);
            document.getElementById('processSteps').innerHTML = processHtml;

            // 6. 输出结果到控制台
            console.log('=== 木箱开料计算结果 ===');
            console.log(`输入参数: C=${C}mm, K=${K}mm, G=${G}mm, H=${H}mm`);
            console.log(`适用情况: 情况${result.situation}`);
            console.log('开料清单:');
            result.parts.forEach(part => {
                console.log(`${part.name}: ${part.length}mm × ${part.width}mm × ${part.qty}个`);
            });
            console.log('========================');
        }

        // 页面加载时自动计算一次
        window.onload = calculate;
    </script>
</body>
</html>
