# 木箱开料计算规则

## 输入参数
- **C**: 长度 (mm)
- **K**: 宽度 (mm) 
- **G**: 高度 (mm)
- **H**: 厚度 (mm)

## 计算规则分类

根据长度C的不同范围，分为三种情况：

### 情况1：C < 1500mm

#### 主要板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 上下板 | (C+2H) × (K+2H) | 2个 |
| 左右板 | (C+2H) × G | 2个 |
| 前后板 | K × G | 2个 |

#### 辅助板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 箱子支撑竖版 | (10H+K) × 50 | 4个 |
| 盖子手抓板 | (K+2H) × 50 | 2个 |
| 支腿 | (K+2H) × 50 | 2个 |

---

### 情况2：1500mm ≤ C < 2440mm

#### 主要板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 上下板 | (C+4H) × (K+2H) | 2个 |
| 左右板 | (C+4H) × G | 2个 |
| 前后板 | K × G | 4个 |

#### 辅助板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 箱子支撑竖版 | (10H+K) × 50 | 6个 |
| 盖子手抓板 | (K+2H) × 50 | 3个 |
| 支腿 | (K+2H) × 50 | 3个 |

---

### 情况3：C ≥ 2440mm

#### 主要板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 上下板 | (C+4H) × (K+2H) | 2个 |
| 左右板 | (C+4H) × G | 2个 |
| 前后板 | K × G | 4个 |

#### 辅助板材
| 板材名称 | 尺寸计算公式 | 数量 |
|---------|-------------|------|
| 箱子支撑竖版 | (10H+K) × 50 | 8个 |
| 盖子手抓板 | (K+2H) × 50 | 4个 |
| 支腿 | (K+2H) × 50 | 4个 |

---

## 规律总结

### 主要板材变化规律：
1. **情况1 (C<1500)**：上下板和左右板使用 `C+2H`
2. **情况2和3 (C≥1500)**：上下板和左右板使用 `C+4H`
3. **前后板数量**：情况1为2个，情况2和3为4个

### 辅助板材数量变化：
- **箱子支撑竖版**：4个 → 6个 → 8个
- **盖子手抓板**：2个 → 3个 → 4个  
- **支腿**：2个 → 3个 → 4个

### 关键分界点：
- **1500mm**：主要板材计算公式改变，辅助板材数量增加
- **2440mm**：辅助板材数量进一步增加

## 计算示例

### 示例1：C=1200mm, K=800mm, G=600mm, H=18mm
**适用情况1**
- 上下板：(1200+36) × (800+36) = 1236 × 836，2个
- 左右板：(1200+36) × 600 = 1236 × 600，2个
- 前后板：800 × 600，2个
- 箱子支撑竖版：(180+800) × 50 = 980 × 50，4个
- 盖子手抓板：(800+36) × 50 = 836 × 50，2个
- 支腿：(800+36) × 50 = 836 × 50，2个

### 示例2：C=2000mm, K=800mm, G=600mm, H=18mm  
**适用情况2**
- 上下板：(2000+72) × (800+36) = 2072 × 836，2个
- 左右板：(2000+72) × 600 = 2072 × 600，2个
- 前后板：800 × 600，4个
- 箱子支撑竖版：(180+800) × 50 = 980 × 50，6个
- 盖子手抓板：(800+36) × 50 = 836 × 50，3个
- 支腿：(800+36) × 50 = 836 × 50，3个
