# 盖帽式开料计算示例

## 📋 概述

本文档演示了家具制作中"盖帽式"结构的开料计算方法。这种结构的特点是顶板和底板像帽子一样盖在侧板框架的上面和下面，提供更好的结构强度和美观度。

## 🏗️ 结构特点

### 盖帽式设计原理
- **核心概念**：顶板和底板完全覆盖侧板的端面
- **结构优势**：
  - 提供更好的结构强度
  - 外观更加美观
  - 顶/底板端面不会暴露
  - 适合制作抽屉、柜体等家具部件

### 组装示意图
```
顶板 (盖帽层)
├─────────────┤
│  内部空间    │
│             │
├─────────────┤
底板 (盖帽层)
```

## 📐 计算逻辑

### 输入参数
- **L**：内部长度
- **W**：内部宽度  
- **H**：内部高度
- **T**：板材厚度

### 计算公式

| 板件 | 长度计算 | 宽度/高度计算 | 数量 |
|------|----------|---------------|------|
| 顶板 | L | W + 2×T | 1 |
| 底板 | L | W + 2×T | 1 |
| 长侧板 | L | H | 2 |
| 宽侧板 | W | H | 2 |

### 关键计算说明
- **顶/底板宽度 = W + 2×T**：需要覆盖两个宽侧板的厚度
- **侧板尺寸 = 内部尺寸**：形成内部框架结构

## 🔢 计算实例

### 项目需求
制作一个小抽屉，规格如下：

| 参数 | 数值 |
|------|------|
| 内部长度 (L) | 400mm |
| 内部宽度 (W) | 300mm |
| 内部高度 (H) | 100mm |
| 板材厚度 (T) | 18mm |

### 计算过程

#### 第一步：计算顶板/底板尺寸
- **长度** = L = 400mm
- **宽度** = W + 2×T = 300 + 2×18 = 300 + 36 = **336mm**

#### 第二步：计算长侧板尺寸
- **长度** = L = 400mm
- **高度** = H = 100mm

#### 第三步：计算宽侧板尺寸
- **宽度** = W = 300mm
- **高度** = H = 100mm

### 最终开料清单

| 板件名称 | 尺寸规格 | 数量 | 备注 |
|---------|---------|------|------|
| 顶板 | 400mm × 336mm | 1块 | 盖帽层 |
| 底板 | 400mm × 336mm | 1块 | 盖帽层 |
| 长侧板 | 400mm × 100mm | 2块 | 框架结构 |
| 宽侧板 | 300mm × 100mm | 2块 | 框架结构 |

## 🔍 验证说明

### 组装后验证
- **内部长度**：400mm ✓
- **内部宽度**：300mm ✓ (336mm - 18mm×2 = 300mm)
- **内部高度**：100mm ✓

### 结构验证
```
顶板 (400×336)
├─ 完全覆盖 ─┤
长侧板        宽侧板        宽侧板        长侧板
(400×100)    (300×100)    (300×100)    (400×100)
├─ 完全覆盖 ─┤
底板 (400×336)
```

## 💡 应用场景

这种计算方法适用于：
- 抽屉制作
- 柜体内部分隔
- 储物盒制作
- 定制家具部件
- 木工DIY项目

## 📝 注意事项

1. **板材厚度一致性**：确保所有板材厚度相同
2. **切割精度**：保证切割尺寸准确
3. **组装顺序**：先组装侧板框架，再安装顶/底板
4. **预留间隙**：根据实际需要可适当调整尺寸预留组装间隙

---

*文档创建日期：2025年9月6日*  
*适用于：家具制作、木工计算、定制加工*
